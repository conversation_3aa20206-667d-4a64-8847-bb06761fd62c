import { NextResponse } from 'next/server';

export interface BackpackApiResponse {
  connected: boolean;
  price: number;
  timestamp: string;
}

export async function GET() {
  try {
    const response = await fetch('http://47.245.62.204:8080/api/backpack', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      // 添加超时和缓存控制
      next: { revalidate: 1 }, // 1秒缓存
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data: BackpackApiResponse = await response.json();
    
    return NextResponse.json({
      success: true,
      data,
    });
  } catch (error) {
    console.error('Error fetching Backpack price:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch Backpack price',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
