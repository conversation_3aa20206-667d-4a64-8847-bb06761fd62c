import { NextResponse } from 'next/server';

export interface BackpackApiResponse {
  connected: boolean;
  price: number;
  timestamp: string;
}

// 带重试的 fetch 函数
async function fetchWithRetry(url: string, maxRetries = 2, timeout = 8000) {
  let lastError: Error;

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), timeout);

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'Mozilla/5.0 (compatible; PriceMonitor/1.0)',
        },
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return response;
    } catch (error) {
      lastError = error as Error;
      console.log(`Backpack API attempt ${attempt + 1} failed:`, error);

      // 如果不是最后一次尝试，等待后重试
      if (attempt < maxRetries) {
        const delay = Math.min(1000 * Math.pow(2, attempt), 5000); // 指数退避，最大5秒
        console.log(`Retrying in ${delay}ms...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }

  throw lastError!;
}

export async function GET() {
  try {
    const response = await fetchWithRetry(
      'http://47.245.62.204:8080/api/backpack',
      2, // 最多重试2次
      8000 // 8秒超时
    );

    const data: BackpackApiResponse = await response.json();

    return NextResponse.json({
      success: true,
      data,
    });
  } catch (error) {
    console.error('Error fetching Backpack price after retries:', error);

    // 根据错误类型返回不同的错误信息
    let errorMessage = 'Unknown error';
    let statusCode = 500;

    if (error instanceof Error) {
      if (error.name === 'AbortError') {
        errorMessage = 'Request timeout - API is taking too long to respond';
        statusCode = 408;
      } else if (error.message.includes('UND_ERR_SOCKET') || error.message.includes('ECONNRESET')) {
        errorMessage = 'Network connection failed - API server may be down';
        statusCode = 503;
      } else if (error.message.includes('HTTP error')) {
        errorMessage = error.message;
        statusCode = 502;
      } else {
        errorMessage = error.message;
      }
    }

    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch Backpack price',
        message: errorMessage,
      },
      { status: statusCode }
    );
  }
}
