import { LighterPrice, BackpackApiResponse, PriceData } from '@/types/price';

// Lighter API 数据获取
export async function fetchLighterPrice(): Promise<PriceData | null> {
  try {
    const response = await fetch('/api/lighter');
    const result = await response.json();

    if (!result.success) {
      throw new Error(result.message || 'Failed to fetch Lighter price');
    }

    const lighterData: LighterPrice = result.data;

    return {
      exchange: 'lighter',
      symbol: 'BTC/USD',
      price: lighterData.mid_price,
      timestamp: new Date(lighterData.timestamp).getTime(),
      connected: lighterData.connected,
    };
  } catch (error) {
    console.error('Error fetching Lighter price:', error);
    return null;
  }
}

// Backpack API 数据获取
export async function fetchBackpackPrice(): Promise<PriceData | null> {
  try {
    const response = await fetch('/api/backpack');
    const result = await response.json();

    if (!result.success) {
      throw new Error(result.message || 'Failed to fetch Backpack price');
    }

    const backpackData: BackpackApiResponse = result.data;

    return {
      exchange: 'backpack',
      symbol: 'BTC/USD',
      price: backpackData.price,
      timestamp: new Date(backpackData.timestamp).getTime(),
      connected: backpackData.connected,
    };
  } catch (error) {
    console.error('Error fetching Backpack price:', error);
    return null;
  }
}

// 币安 WebSocket 连接类
export class BinanceWebSocket {
  private ws: WebSocket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;
  
  constructor(private onPriceUpdate: (data: PriceData) => void) {}
  
  connect() {
    try {
      // 使用币安期货 WebSocket 获取 BTC/USDC 永续合约价格
      this.ws = new WebSocket('wss://fstream.binance.com/ws/btcusdc@ticker');

      this.ws.onopen = () => {
        console.log('Binance Futures WebSocket connected');
        this.reconnectAttempts = 0;
      };

      this.ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          const priceData: PriceData = {
            exchange: 'binance',
            symbol: 'BTC/USDC (永续)',
            price: parseFloat(data.c),
            timestamp: data.E,
            connected: true,
            change24h: parseFloat(data.p),
            changePercent24h: parseFloat(data.P),
          };
          this.onPriceUpdate(priceData);
        } catch (error) {
          console.error('Error parsing Binance Futures data:', error);
        }
      };
      
      this.ws.onclose = () => {
        console.log('Binance Futures WebSocket disconnected');
        this.handleReconnect();
      };

      this.ws.onerror = (error) => {
        console.error('Binance Futures WebSocket error:', error);
      };
    } catch (error) {
      console.error('Error connecting to Binance Futures WebSocket:', error);
    }
  }
  
  private handleReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      setTimeout(() => {
        console.log(`Attempting to reconnect to Binance Futures (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
        this.connect();
      }, this.reconnectDelay * this.reconnectAttempts);
    }
  }
  
  disconnect() {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
  }
}


