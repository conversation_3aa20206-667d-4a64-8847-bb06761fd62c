import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Calendar, Clock } from 'lucide-react';

interface TimeRangeSelectorProps {
  onRangeChange: (range: {
    count?: number;
    startTime?: string;
    endTime?: string;
  }) => void;
  loading?: boolean;
}

export function TimeRangeSelector({ onRangeChange, loading }: TimeRangeSelectorProps) {
  const [mode, setMode] = useState<'count' | 'range'>('count');
  const [count, setCount] = useState(1000);
  const [startTime, setStartTime] = useState('');
  const [endTime, setEndTime] = useState('');

  const handleQuickSelect = (hours: number) => {
    const now = new Date();
    const start = new Date(now.getTime() - hours * 60 * 60 * 1000);

    // 使用本地时间格式，避免时区问题
    const formatDateTime = (date: Date) => {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const seconds = String(date.getSeconds()).padStart(2, '0');
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    };

    const startTimeStr = formatDateTime(start);
    const endTimeStr = formatDateTime(now);

    console.log('TimeRangeSelector quick select:', {
      hours,
      now: now.toLocaleString('zh-CN'),
      start: start.toLocaleString('zh-CN'),
      startTimeStr,
      endTimeStr
    });

    setMode('range');
    setStartTime(startTimeStr);
    setEndTime(endTimeStr);

    onRangeChange({
      startTime: startTimeStr,
      endTime: endTimeStr
    });
  };

  const handleCountChange = () => {
    onRangeChange({ count });
  };

  const handleRangeChange = () => {
    if (startTime && endTime) {
      onRangeChange({
        startTime,
        endTime
      });
    }
  };

  const formatDateTimeLocal = (dateTimeString: string) => {
    if (!dateTimeString) return '';
    return dateTimeString.replace(' ', 'T');
  };

  const formatDateTimeForAPI = (dateTimeLocal: string) => {
    if (!dateTimeLocal) return '';
    return dateTimeLocal.replace('T', ' ');
  };

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button variant="outline" size="sm" className="h-7 px-2 text-xs">
          <Calendar className="h-3 w-3 mr-1" />
          时间范围
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80" align="start">
        <div className="space-y-4">
          <div className="space-y-2">
            <Label className="text-sm font-medium">快速选择</Label>
            <div className="grid grid-cols-3 gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleQuickSelect(1)}
                disabled={loading}
              >
                最近1小时
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleQuickSelect(6)}
                disabled={loading}
              >
                最近6小时
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleQuickSelect(24)}
                disabled={loading}
              >
                最近24小时
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleQuickSelect(72)}
                disabled={loading}
              >
                最近3天
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleQuickSelect(168)}
                disabled={loading}
              >
                最近7天
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleQuickSelect(720)}
                disabled={loading}
              >
                最近30天
              </Button>
            </div>
          </div>

          <div className="border-t pt-4">
            <div className="flex items-center gap-2 mb-3">
              <Button
                variant={mode === 'count' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setMode('count')}
              >
                按数量
              </Button>
              <Button
                variant={mode === 'range' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setMode('range')}
              >
                按时间
              </Button>
            </div>

            {mode === 'count' ? (
              <div className="space-y-3">
                <div>
                  <Label htmlFor="count" className="text-sm">记录数量</Label>
                  <Input
                    id="count"
                    type="number"
                    value={count}
                    onChange={(e) => setCount(parseInt(e.target.value) || 1000)}
                    placeholder="1000"
                    min="1"
                    max="10000"
                    className="mt-1"
                  />
                  <p className="text-xs text-gray-500 mt-1">最多10000条记录</p>
                </div>
                <Button
                  onClick={handleCountChange}
                  disabled={loading}
                  size="sm"
                  className="w-full"
                >
                  <Clock className="h-3 w-3 mr-1" />
                  获取最新 {count} 条
                </Button>
              </div>
            ) : (
              <div className="space-y-3">
                <div>
                  <Label htmlFor="start-time" className="text-sm">开始时间</Label>
                  <Input
                    id="start-time"
                    type="datetime-local"
                    value={formatDateTimeLocal(startTime)}
                    onChange={(e) => setStartTime(formatDateTimeForAPI(e.target.value))}
                    className="mt-1"
                  />
                </div>
                <div>
                  <Label htmlFor="end-time" className="text-sm">结束时间</Label>
                  <Input
                    id="end-time"
                    type="datetime-local"
                    value={formatDateTimeLocal(endTime)}
                    onChange={(e) => setEndTime(formatDateTimeForAPI(e.target.value))}
                    className="mt-1"
                  />
                </div>
                <Button
                  onClick={handleRangeChange}
                  disabled={loading || !startTime || !endTime}
                  size="sm"
                  className="w-full"
                >
                  <Calendar className="h-3 w-3 mr-1" />
                  查询时间范围
                </Button>
              </div>
            )}
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
}
