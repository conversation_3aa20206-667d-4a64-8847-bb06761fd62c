'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Settings, Save, Eye, EyeOff, X } from 'lucide-react';
import { toast } from 'sonner';

export function SettingsModal() {
  const [open, setOpen] = useState(false);
  const [barkApiKey, setBarkApiKey] = useState('');
  const [showApiKey, setShowApiKey] = useState(false);
  const [loading, setSaving] = useState(false);
  const [enableNormalNotification, setEnableNormalNotification] = useState(false);

  // 从本地存储加载设置
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const savedApiKey = localStorage.getItem('bark_api_key');
      if (savedApiKey) {
        setBarkApiKey(savedApiKey);
      }

      const savedNormalNotification = localStorage.getItem('enable_normal_notification');
      if (savedNormalNotification) {
        setEnableNormalNotification(savedNormalNotification === 'true');
      }
    }
  }, []);

  // 保存设置到本地存储
  const handleSave = async () => {
    setSaving(true);
    try {
      if (typeof window !== 'undefined') {
        if (barkApiKey.trim()) {
          localStorage.setItem('bark_api_key', barkApiKey.trim());
        } else {
          localStorage.removeItem('bark_api_key');
        }

        // 保存普通通知设置
        localStorage.setItem('enable_normal_notification', enableNormalNotification.toString());

        toast.success('设置已保存');
      }
      setOpen(false);
    } catch (error) {
      toast.error('保存失败，请重试');
    } finally {
      setSaving(false);
    }
  };

  // 测试通知
  const handleTest = async () => {
    try {
      const title = 'BTC价格监控测试';
      const message = '测试通知发送成功！系统运行正常。';

      // 如果启用了普通通知，发送浏览器通知
      if (enableNormalNotification) {
        if ('Notification' in window) {
          if (Notification.permission === 'granted') {
            new Notification(title, {
              body: message,
              icon: '/favicon.ico'
            });
            toast.success('普通通知已发送');
          } else if (Notification.permission !== 'denied') {
            const permission = await Notification.requestPermission();
            if (permission === 'granted') {
              new Notification(title, {
                body: message,
                icon: '/favicon.ico'
              });
              toast.success('普通通知已发送');
            } else {
              toast.error('普通通知权限被拒绝');
            }
          } else {
            toast.error('普通通知权限被拒绝');
          }
        } else {
          toast.error('浏览器不支持普通通知');
        }
      }

      // 如果有 Bark API Key，发送 Bark 通知
      if (barkApiKey.trim()) {
        const url = `https://api.day.app/${barkApiKey.trim()}/${encodeURIComponent(title)}/${encodeURIComponent(message)}?level=critical&volume=10`;

        const response = await fetch(url, {
          method: 'POST'
        });

        if (response.ok) {
          toast.success('Bark 通知已发送');
        } else {
          toast.error('Bark 通知发送失败');
        }
      }

      // 如果两种通知都没有启用
      if (!enableNormalNotification && !barkApiKey.trim()) {
        toast.error('请先启用至少一种通知方式');
      }
    } catch {
      toast.error('测试通知发送失败');
    }
  };

  return (
    <>
      <Button
        variant="outline"
        size="sm"
        className="h-7 px-2"
        onClick={() => setOpen(true)}
      >
        <Settings className="h-3 w-3 mr-1" />
        设置
      </Button>

      {open && (
        <div className="fixed inset-0 z-50 flex items-center justify-center">
          {/* 背景遮罩 */}
          <div
            className="fixed inset-0 bg-black/50"
            onClick={() => setOpen(false)}
          />

          {/* 模态框内容 */}
          <div className="relative bg-white rounded-lg shadow-lg w-full max-w-md mx-4 p-6">
            {/* 关闭按钮 */}
            <button
              onClick={() => setOpen(false)}
              className="absolute right-4 top-4 rounded-sm opacity-70 hover:opacity-100 transition-opacity"
            >
              <X className="h-4 w-4" />
            </button>

            {/* 标题 */}
            <div className="flex items-center gap-2 mb-6">
              <Settings className="h-5 w-5" />
              <h2 className="text-lg font-semibold">通知设置</h2>
            </div>

            {/* 表单内容 */}
            <div className="space-y-4">
              {/* 普通通知设置 */}
              <div className="space-y-2">
                <label className="text-sm font-medium">通知方式</label>
                <div className="flex items-center space-x-2">
                  <input
                    id="normal-notification"
                    type="checkbox"
                    checked={enableNormalNotification}
                    onChange={(e) => setEnableNormalNotification(e.target.checked)}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="normal-notification" className="text-sm text-gray-700">
                    启用普通通知（浏览器通知）
                  </label>
                </div>
                <p className="text-xs text-gray-500">
                  勾选后将发送浏览器原生通知，需要授权通知权限
                </p>
              </div>

              {/* Bark API Key 设置 */}
              <div className="space-y-2">
                <label htmlFor="bark-api-key" className="text-sm font-medium">
                  Bark API Key（可选）
                </label>
                <div className="relative">
                  <input
                    id="bark-api-key"
                    type={showApiKey ? 'text' : 'password'}
                    placeholder="请输入 Bark API Key"
                    value={barkApiKey}
                    onChange={(e) => setBarkApiKey(e.target.value)}
                    className="flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm pr-10 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                  <button
                    type="button"
                    className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-gray-100 rounded-r-md"
                    onClick={() => setShowApiKey(!showApiKey)}
                  >
                    {showApiKey ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </button>
                </div>
                <p className="text-xs text-gray-500">
                  用于发送手机推送通知。获取方式：下载 Bark App 并复制 API Key
                </p>
              </div>

              <div className="flex gap-2 pt-2">
                <Button
                  onClick={handleTest}
                  variant="outline"
                  className="flex-1"
                  disabled={!enableNormalNotification && !barkApiKey.trim()}
                >
                  测试通知
                </Button>
                <Button
                  onClick={handleSave}
                  className="flex-1"
                  disabled={loading}
                >
                  <Save className="h-4 w-4 mr-2" />
                  {loading ? '保存中...' : '保存'}
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
