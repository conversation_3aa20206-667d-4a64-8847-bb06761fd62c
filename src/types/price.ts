// 价格数据类型定义

export interface LighterPrice {
  best_ask: number;
  best_bid: number;
  connected: boolean;
  mid_price: number;
  spread: number;
  timestamp: string;
}

export interface BinancePrice {
  symbol: string;
  price: string;
  timestamp: number;
}

export interface BackpackPrice {
  symbol: string;
  price: number;
  timestamp: number;
}

export interface BackpackApiResponse {
  connected: boolean;
  price: number;
  timestamp: string;
}

export interface PriceData {
  exchange: 'binance' | 'backpack' | 'lighter';
  symbol: string;
  price: number;
  timestamp: number;
  connected: boolean;
  change24h?: number;
  changePercent24h?: number;
}

export interface PriceState {
  binance: PriceData | null;
  backpack: PriceData | null;
  lighter: PriceData | null;
}

export interface WebSocketMessage {
  type: 'price_update' | 'connection_status';
  data: any;
}
